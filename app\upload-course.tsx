import { geminiService } from "@/lib/gemini";
import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function UploadCourseScreen() {
  const { user } = useUser();
  const [selectedFile, setSelectedFile] =
    useState<DocumentPicker.DocumentPickerResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState("");

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "text/plain",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile(result);
      }
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document");
    }
  };

  const readFileContent = async (uri: string): Promise<string> => {
    try {
      const content = await FileSystem.readAsStringAsync(uri);
      return content;
    } catch (error) {
      console.error("Error reading file:", error);
      throw new Error("Failed to read file content");
    }
  };

  const processCourse = async () => {
    if (
      !selectedFile ||
      !selectedFile.assets ||
      selectedFile.assets.length === 0
    ) {
      Alert.alert("Error", "Please select a file first");
      return;
    }

    if (!user) {
      Alert.alert("Error", "Please sign in to upload courses");
      return;
    }

    setIsProcessing(true);

    try {
      const file = selectedFile.assets[0];

      setProcessingStep("Reading file content...");
      const fileContent = await readFileContent(file.uri);

      setProcessingStep("Extracting text with AI...");
      const extractedText = await geminiService.extractTextFromDocument(
        fileContent,
        file.mimeType || "text/plain"
      );

      setProcessingStep("Analyzing course content...");
      const courseAnalysis = await geminiService.analyzeCourse(extractedText);

      setProcessingStep("Saving course to database...");

      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user.id)
        .single();

      if (!supabaseUser) {
        throw new Error("User not found in database");
      }

      // Save course to database
      const { data: course, error: courseError } = await supabase
        .from("courses")
        .insert({
          user_id: supabaseUser.id,
          title: courseAnalysis.title,
          description: courseAnalysis.description,
          syllabus_content: extractedText,
          difficulty_level: courseAnalysis.difficulty,
          estimated_duration_weeks: courseAnalysis.estimatedWeeks,
        })
        .select()
        .single();

      if (courseError) {
        throw courseError;
      }

      setProcessingStep("Generating study plan...");
      const studyPlan = await geminiService.generateStudyPlan(courseAnalysis);

      // Save study plan
      const { error: studyPlanError } = await supabase
        .from("study_plans")
        .insert({
          course_id: course.id,
          user_id: supabaseUser.id,
          title: studyPlan.title,
          description: studyPlan.description,
          milestones: studyPlan.milestones,
          start_date: new Date().toISOString().split("T")[0],
          target_completion_date: new Date(
            Date.now() + studyPlan.totalWeeks * 7 * 24 * 60 * 60 * 1000
          )
            .toISOString()
            .split("T")[0],
          status: "active",
        });

      if (studyPlanError) {
        console.error("Error saving study plan:", studyPlanError);
      }

      setProcessingStep("Generating flashcards...");
      const flashcardSets = await geminiService.generateFlashcards(
        courseAnalysis
      );

      // Save flashcards
      const flashcardsToInsert = flashcardSets.flatMap((set) =>
        set.cards.map((card) => ({
          course_id: course.id,
          user_id: supabaseUser.id,
          front: card.front,
          back: card.back,
          category: set.category,
          difficulty: card.difficulty,
          next_review_date: new Date().toISOString(),
          review_count: 0,
          ease_factor: 2.5,
          interval_days: 1,
        }))
      );

      if (flashcardsToInsert.length > 0) {
        const { error: flashcardsError } = await supabase
          .from("flashcards")
          .insert(flashcardsToInsert);

        if (flashcardsError) {
          console.error("Error saving flashcards:", flashcardsError);
        }
      }

      Alert.alert(
        "Success!",
        `Course "${courseAnalysis.title}" has been uploaded and processed successfully!`,
        [
          {
            text: "View Course",
            onPress: () => router.replace(`/course/${course.id}`),
          },
          {
            text: "Go to Dashboard",
            onPress: () => router.replace("/(tabs)"),
          },
        ]
      );
    } catch (error) {
      console.error("Error processing course:", error);
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Failed to process course"
      );
    } finally {
      setIsProcessing(false);
      setProcessingStep("");
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Upload Course</Text>
      </View>

      <View style={styles.content}>
        {/* LEVEL 1: Hero Introduction */}
        <View style={styles.heroSection}>
          <Text style={styles.title}>🎓 Upload Your Course</Text>
          <Text style={styles.subtitle}>
            Transform Learning Materials into Smart Study Plans
          </Text>
          <Text style={styles.description}>
            Upload any course syllabus, textbook, or learning material and our
            AI will create a personalized study plan with flashcards,
            milestones, and spaced repetition schedules.
          </Text>
        </View>

        {/* Visual Separator */}
        <View style={styles.levelSeparator} />

        {/* LEVEL 2: Primary Action Section */}
        <View style={styles.primaryActionSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📤 Get Started</Text>
            <Text style={styles.sectionSubtitle}>
              Upload your document to begin
            </Text>
          </View>

          <View style={styles.uploadSection}>
            <TouchableOpacity
              style={[
                styles.uploadButton,
                selectedFile && styles.uploadButtonSelected,
              ]}
              onPress={pickDocument}
              disabled={isProcessing}
            >
              <Text style={styles.uploadButtonIcon}>
                {selectedFile ? "✓" : "📁"}
              </Text>
              <Text style={styles.uploadButtonText}>
                {selectedFile
                  ? "File Selected - Tap to Change"
                  : "Select Document"}
              </Text>
            </TouchableOpacity>

            {selectedFile &&
              selectedFile.assets &&
              selectedFile.assets.length > 0 && (
                <View style={styles.fileInfo}>
                  <View style={styles.fileDetails}>
                    <Text style={styles.fileName}>
                      📄 {selectedFile.assets[0].name}
                    </Text>
                    <Text style={styles.fileSize}>
                      Size:{" "}
                      {Math.round((selectedFile.assets[0].size || 0) / 1024)} KB
                    </Text>
                    <Text style={styles.fileType}>
                      Type:{" "}
                      {selectedFile.assets[0].mimeType
                        ?.split("/")[1]
                        ?.toUpperCase() || "Unknown"}
                    </Text>
                  </View>
                </View>
              )}

            <View style={styles.supportedFormats}>
              <Text style={styles.supportedTitle}>📋 Supported File Types</Text>
              <View style={styles.formatsList}>
                <View style={styles.formatItem}>
                  <Text style={styles.formatIcon}>📄</Text>
                  <Text style={styles.formatText}>PDF Documents</Text>
                </View>
                <View style={styles.formatItem}>
                  <Text style={styles.formatIcon}>📝</Text>
                  <Text style={styles.formatText}>
                    Word Documents (DOC, DOCX)
                  </Text>
                </View>
                <View style={styles.formatItem}>
                  <Text style={styles.formatIcon}>📃</Text>
                  <Text style={styles.formatText}>Plain Text Files (TXT)</Text>
                </View>
              </View>
              <Text style={styles.formatNote}>
                💡 Tip: For best results, use documents with clear structure and
                headings
              </Text>
            </View>

            {selectedFile && (
              <TouchableOpacity
                style={[
                  styles.processButton,
                  isProcessing && styles.processButtonDisabled,
                ]}
                onPress={processCourse}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <View style={styles.processingContainer}>
                    <ActivityIndicator color="#fff" size="small" />
                    <Text style={styles.processButtonText}>
                      {processingStep}
                    </Text>
                  </View>
                ) : (
                  <View style={styles.processButtonContent}>
                    <Text style={styles.processButtonIcon}>🚀</Text>
                    <Text style={styles.processButtonText}>
                      Start AI Processing
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Visual Separator */}
        <View style={styles.levelSeparator} />

        {/* LEVEL 3: Features Overview */}
        <View style={styles.featuresSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>✨ What You'll Get</Text>
            <Text style={styles.sectionSubtitle}>
              Powerful AI-driven learning tools
            </Text>
          </View>

          <View style={styles.featuresGrid}>
            <View style={styles.featureCard}>
              <Text style={styles.featureIcon}>🤖</Text>
              <Text style={styles.featureTitle}>AI-Powered Analysis</Text>
              <Text style={styles.featureText}>
                Advanced AI extracts key concepts and learning objectives
              </Text>
            </View>
            <View style={styles.featureCard}>
              <Text style={styles.featureIcon}>📚</Text>
              <Text style={styles.featureTitle}>Smart Flashcards</Text>
              <Text style={styles.featureText}>
                Auto-generated flashcards with difficulty levels
              </Text>
            </View>
            <View style={styles.featureCard}>
              <Text style={styles.featureIcon}>📅</Text>
              <Text style={styles.featureTitle}>Study Planning</Text>
              <Text style={styles.featureText}>
                Personalized timeline with achievable milestones
              </Text>
            </View>
            <View style={styles.featureCard}>
              <Text style={styles.featureIcon}>🧠</Text>
              <Text style={styles.featureTitle}>Spaced Repetition</Text>
              <Text style={styles.featureText}>
                Scientifically-proven memory retention system
              </Text>
            </View>
          </View>
        </View>

        {/* Visual Separator */}
        <View style={styles.levelSeparator} />

        {/* LEVEL 4: Process Details */}
        <View style={styles.processSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>🔄 How It Works</Text>
            <Text style={styles.sectionSubtitle}>
              Our AI processing pipeline transforms your document
            </Text>
          </View>

          <View style={styles.processSteps}>
            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepIconContainer}>
                  <Text style={styles.stepIcon}>🔍</Text>
                </View>
                <View style={styles.stepDetails}>
                  <Text style={styles.stepTitle}>Content Analysis</Text>
                  <Text style={styles.stepDescription}>
                    AI extracts and analyzes key concepts, topics, and learning
                    objectives from your document
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepIconContainer}>
                  <Text style={styles.stepIcon}>📋</Text>
                </View>
                <View style={styles.stepDetails}>
                  <Text style={styles.stepTitle}>Study Plan Generation</Text>
                  <Text style={styles.stepDescription}>
                    Creates a personalized timeline with weekly milestones and
                    achievable goals
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepIconContainer}>
                  <Text style={styles.stepIcon}>🎯</Text>
                </View>
                <View style={styles.stepDetails}>
                  <Text style={styles.stepTitle}>Smart Flashcards</Text>
                  <Text style={styles.stepDescription}>
                    Generates targeted flashcards with varying difficulty levels
                    for optimal learning
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>4</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepIconContainer}>
                  <Text style={styles.stepIcon}>⏰</Text>
                </View>
                <View style={styles.stepDetails}>
                  <Text style={styles.stepTitle}>Spaced Repetition</Text>
                  <Text style={styles.stepDescription}>
                    Implements scientifically-proven scheduling for maximum
                    memory retention
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Visual Separator */}
        <View style={styles.levelSeparator} />

        {/* LEVEL 5: Benefits & Social Proof */}
        <View style={styles.benefitsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>🏆 Why Choose AI Learning?</Text>
            <Text style={styles.sectionSubtitle}>
              Join thousands of successful learners
            </Text>
          </View>

          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>⚡</Text>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>70% Time Saved</Text>
                <Text style={styles.benefitText}>
                  Automated study planning eliminates manual work
                </Text>
              </View>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>🎯</Text>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Focused Learning</Text>
                <Text style={styles.benefitText}>
                  AI identifies what matters most for your success
                </Text>
              </View>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>📈</Text>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>50% Better Retention</Text>
                <Text style={styles.benefitText}>
                  Spaced repetition maximizes memory consolidation
                </Text>
              </View>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>🏆</Text>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Faster Results</Text>
                <Text style={styles.benefitText}>
                  Achieve your learning goals in record time
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  content: {
    padding: 20,
  },

  // Visual Separators
  levelSeparator: {
    height: 2,
    backgroundColor: "#e9ecef",
    marginVertical: 16,
    borderRadius: 1,
    opacity: 0.5,
  },
  // LEVEL 1: Hero Section
  heroSection: {
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 32,
    marginBottom: 32,
    borderWidth: 2,
    borderColor: "#007AFF",
    position: "relative",
  },
  title: {
    fontSize: 34,
    fontWeight: "900",
    color: "#1a1a1a",
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#007AFF",
    marginBottom: 16,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    color: "#495057",
    lineHeight: 26,
    textAlign: "center",
  },

  // Section Headers (Universal)
  sectionHeader: {
    marginBottom: 20,
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "800",
    color: "#1a1a1a",
    marginBottom: 6,
    textAlign: "center",
  },
  sectionSubtitle: {
    fontSize: 16,
    color: "#6c757d",
    textAlign: "center",
    lineHeight: 22,
  },

  // LEVEL 2: Primary Action Section
  primaryActionSection: {
    backgroundColor: "#fff",
    borderRadius: 18,
    padding: 28,
    marginBottom: 28,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },

  // LEVEL 3: Features Section
  featuresSection: {
    backgroundColor: "#f8f9fa",
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#dee2e6",
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  featureCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    width: "48%",
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#e9ecef",
    alignItems: "center",
  },
  featureIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
    textAlign: "center",
  },
  featureText: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
    lineHeight: 16,
  },
  uploadSection: {
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: "#dee2e6",
  },

  // LEVEL 4: Process Section
  processSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  processSteps: {
    marginTop: 8,
  },
  processStep: {
    flexDirection: "row",
    marginBottom: 24,
    alignItems: "flex-start",
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#007AFF",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
    marginTop: 4,
  },
  stepNumberText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
  },
  stepContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-start",
  },
  stepIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#f8f9fa",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  stepIcon: {
    fontSize: 20,
  },
  stepDetails: {
    flex: 1,
    paddingTop: 4,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 6,
  },
  stepDescription: {
    fontSize: 14,
    color: "#6c757d",
    lineHeight: 20,
  },
  uploadButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "#007AFF",
  },
  uploadButtonSelected: {
    backgroundColor: "#28a745",
    borderColor: "#28a745",
  },
  uploadButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  uploadButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  fileInfo: {
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  fileDetails: {
    alignItems: "flex-start",
  },
  fileName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 6,
  },
  fileSize: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  fileType: {
    fontSize: 14,
    color: "#666",
  },
  supportedFormats: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  supportedTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#333",
    marginBottom: 16,
    textAlign: "center",
  },
  formatsList: {
    marginBottom: 16,
  },
  formatItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    paddingHorizontal: 12,
  },
  formatIcon: {
    fontSize: 16,
    marginRight: 12,
    width: 20,
  },
  formatText: {
    fontSize: 14,
    color: "#495057",
    fontWeight: "500",
  },
  formatNote: {
    fontSize: 12,
    color: "#6c757d",
    fontStyle: "italic",
    textAlign: "center",
    backgroundColor: "#f8f9fa",
    padding: 12,
    borderRadius: 8,
  },
  processButton: {
    backgroundColor: "#28a745",
    padding: 18,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 30,
    borderWidth: 2,
    borderColor: "#28a745",
  },
  processButtonDisabled: {
    backgroundColor: "#6c757d",
    borderColor: "#6c757d",
  },
  processButtonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  processButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  processButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  processingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },

  // LEVEL 5: Benefits Section
  benefitsSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  benefitsList: {
    marginTop: 8,
  },
  benefitItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
    backgroundColor: "#f8f9fa",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  benefitIcon: {
    fontSize: 24,
    marginRight: 16,
    marginTop: 2,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  benefitText: {
    fontSize: 14,
    color: "#6c757d",
    lineHeight: 20,
  },
});
